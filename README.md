# FinanCred Financeira - Landing Page

Uma landing page moderna e responsiva para a FinanCred Financeira, desenvolvida com HTML5, CSS3 e JavaScript vanilla.

## 🚀 Características

- **Design Minimalista**: Interface limpa e moderna
- **Totalmente Responsivo**: Otimizado para todos os dispositivos
- **Performance Otimizada**: Carregamento rápido e suave
- **Animações Suaves**: Efeitos de scroll e hover elegantes
- **SEO Friendly**: Estrutura semântica e otimizada

## 🎨 Paleta de Cores

- **Vermelho Principal**: #D32F2F
- **Amarelo**: #FFEB3B
- **Branco**: #FFFFFF
- **Verde (WhatsApp)**: #25D366
- **Cinza**: #F5F5F5

## 📁 Estrutura de Arquivos

```
/
├── index.html          # Página principal
├── styles.css          # Estilos CSS
├── script.js           # JavaScript para interatividade
├── logo1.png           # Logo principal (adicionar manualmente)
├── logo2.png           # Logo alternativo (adicionar manualmente)
└── README.md           # Este arquivo
```

## 🖼️ Imagens Necessárias

Adicione as seguintes imagens na pasta raiz do projeto:

- `logo1.png` - Logo principal da FinanCred
- `logo2.png` - Logo alternativo da FinanCred

## 📱 Seções da Página

1. **Header**: Logo da empresa com fundo amarelo
2. **Hero**: Chamada principal com botão de ação
3. **Sobre**: Descrição da empresa
4. **Serviços**: Grid com os serviços oferecidos
5. **Contato**: Informações de contato
6. **Footer**: Rodapé com direitos autorais
7. **WhatsApp Flutuante**: Botão fixo para contato direto

## 🛠️ Tecnologias Utilizadas

- HTML5 semântico
- CSS3 moderno (Flexbox/Grid)
- JavaScript ES6+
- Google Fonts (Poppins)
- Animações CSS puras

## 📞 Informações de Contato

- **WhatsApp**: (86) 99983-4000
- **Instagram**: @finan_cr_ed

## 🌐 Como Usar

1. Faça o download de todos os arquivos
2. Adicione as imagens `logo1.png` e `logo2.png` na pasta raiz
3. Abra o arquivo `index.html` em qualquer navegador moderno
4. A página está pronta para uso!

## 📈 Features Implementadas

- ✅ Design responsivo para mobile-first
- ✅ Animações de scroll suaves
- ✅ Botão WhatsApp flutuante
- ✅ Efeitos hover nos serviços
- ✅ Otimização de performance
- ✅ Lazy loading de imagens
- ✅ Smooth scroll entre seções
- ✅ Animações de entrada dos elementos

## 🎯 Otimizações

- Fonts otimizadas com preconnect
- Animações com RequestAnimationFrame
- Intersection Observer para melhor performance
- CSS minimalista sem frameworks pesados
- JavaScript vanilla para máxima performance

## 📝 Licença

© 2025 FinanCred Financeira. Todos os direitos reservados.
