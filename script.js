// Animações de scroll
document.addEventListener("DOMContentLoaded", function () {
  // Função para verificar se o elemento está visível na tela
  function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
        (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  // Função para adicionar animações de entrada
  function handleScrollAnimations() {
    const elements = document.querySelectorAll(
      ".service-item, .contact-item, .about-text"
    );

    elements.forEach((element) => {
      if (isElementInViewport(element)) {
        element.classList.add("fade-in", "visible");
      }
    });
  }

  // Adicionar animações iniciais
  const serviceItems = document.querySelectorAll(".service-item");
  const contactItems = document.querySelectorAll(".contact-item");
  const aboutText = document.querySelector(".about-text");

  serviceItems.forEach((item, index) => {
    item.classList.add("fade-in");
    item.style.transitionDelay = `${index * 0.1}s`;
  });

  contactItems.forEach((item, index) => {
    item.classList.add("fade-in");
    item.style.transitionDelay = `${index * 0.2}s`;
  });

  if (aboutText) {
    aboutText.classList.add("fade-in");
  }

  // Executar animações no scroll
  window.addEventListener("scroll", handleScrollAnimations);

  // Executar uma vez no carregamento
  handleScrollAnimations();

  // Animação suave para links internos
  const links = document.querySelectorAll('a[href^="#"]');

  links.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href").substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Efeito de hover para os serviços
  const services = document.querySelectorAll(".service-item");

  services.forEach((service) => {
    service.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-5px) scale(1.02)";
    });

    service.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0) scale(1)";
    });
  });

  // Adicionar efeito de parallax sutil no hero
  const hero = document.querySelector(".hero");
  if (hero) {
    window.addEventListener("scroll", function () {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;

      if (scrolled < hero.offsetHeight) {
        hero.style.transform = `translateY(${rate}px)`;
      }
    });
  }

  // Otimização para dispositivos móveis
  let ticking = false;

  function updateAnimations() {
    handleScrollAnimations();
    ticking = false;
  }

  window.addEventListener("scroll", function () {
    if (!ticking) {
      requestAnimationFrame(updateAnimations);
      ticking = true;
    }
  });

  // Preloader simples para as imagens
  const images = document.querySelectorAll("img");

  images.forEach((img) => {
    img.addEventListener("load", function () {
      this.style.opacity = "1";
      this.style.transform = "scale(1)";
    });

    // Se a imagem já estiver carregada
    if (img.complete) {
      img.style.opacity = "1";
      img.style.transform = "scale(1)";
    } else {
      img.style.opacity = "0";
      img.style.transform = "scale(0.95)";
      img.style.transition = "opacity 0.5s ease, transform 0.5s ease";
    }
  });

  // Adicionar funcionalidade ao botão WhatsApp flutuante
  const whatsappButton = document.querySelector(".whatsapp-button");

  if (whatsappButton) {
    whatsappButton.addEventListener("click", function (e) {
      // Adicionar um pequeno feedback visual
      this.style.transform = "scale(0.95)";

      setTimeout(() => {
        this.style.transform = "scale(1)";
      }, 150);
    });
  }

  // Lazy loading para melhor performance
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src || img.src;
          img.classList.remove("lazy");
          imageObserver.unobserve(img);
        }
      });
    });

    const lazyImages = document.querySelectorAll("img[data-src]");
    lazyImages.forEach((img) => imageObserver.observe(img));
  }

  // Adicionar animação de entrada para elementos quando ficam visíveis
  if ("IntersectionObserver" in window) {
    const animateObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible");
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      }
    );

    const animateElements = document.querySelectorAll(".fade-in");
    animateElements.forEach((el) => animateObserver.observe(el));
  }
});

// Função para mostrar/ocultar o botão WhatsApp baseado no scroll
window.addEventListener("scroll", function () {
  const whatsappFloat = document.querySelector(".whatsapp-float");
  const scrollPosition = window.pageYOffset;

  if (scrollPosition > 300) {
    whatsappFloat.style.opacity = "1";
    whatsappFloat.style.visibility = "visible";
  } else {
    whatsappFloat.style.opacity = "0.8";
  }
});

// Adicionar smooth scroll polyfill para navegadores mais antigos
if (!("scrollBehavior" in document.documentElement.style)) {
  const script = document.createElement("script");
  script.src =
    "https://cdn.jsdelivr.net/gh/iamdustan/smoothscroll@0.4.4/dist/smoothscroll.min.js";
  document.head.appendChild(script);
}
