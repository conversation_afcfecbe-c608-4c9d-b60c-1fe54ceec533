/* Reset e configurações globais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #222;
    background: #fff;
    overflow-x: hidden;
    font-weight: 400;
}

/* Container principal */
.container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Header */
.header {
    background: #fffbe7;
    padding: 2.5rem 0 1.5rem 0;
    box-shadow: none;
    border-bottom: 1px solid #f5f5f5;
}

.logo-container {
    text-align: center;
}

.logo {
    max-height: 64px;
    max-width: 220px;
    filter: grayscale(0.1) contrast(1.1);
    opacity: 0.95;
}

/* Hero Section */
.hero {
    background: #fff;
    color: #D32F2F;
    padding: 3.5rem 0 2.5rem 0;
    text-align: center;
}

.hero-title {
    font-size: 2.2rem;
    font-weight: 600;
    margin-bottom: 0.7rem;
    letter-spacing: -1px;
}

.hero-subtitle {
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 2rem;
    color: #444;
    opacity: 0.85;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #25D366;
    color: #fff;
    text-decoration: none;
    padding: 0.85rem 1.7rem;
    border-radius: 32px;
    font-weight: 500;
    font-size: 1.05rem;
    border: none;
    box-shadow: none;
    transition: background 0.2s, transform 0.2s;
}

.cta-button:hover {
    background: #1fa855;
    transform: translateY(-2px) scale(1.03);
}

.whatsapp-icon {
    font-size: 1.2rem;
}

/* About Section */
.about {
    background: #fff;
    padding: 3rem 0 2.5rem 0;
    text-align: center;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #D32F2F;
    margin-bottom: 1.2rem;
    letter-spacing: -0.5px;
    position: relative;
}

.section-title::after {
    content: '';
    width: 40px;
    height: 2px;
    background: #FFEB3B;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

.about-text {
    font-size: 1.05rem;
    max-width: 600px;
    margin: 0 auto;
    color: #555;
    line-height: 1.7;
}

/* Services Section */
.services {
    background: #fafafa;
    padding: 3rem 0 2.5rem 0;
}

.services .section-title {
    margin-bottom: 2rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.service-item {
    background: #fff;
    padding: 1.7rem 1.2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: none;
    border: 1px solid #f0f0f0;
    transition: border 0.2s, background 0.2s;
}

.service-item:hover {
    border: 1.5px solid #D32F2F;
    background: #fff8f8;
}

.service-icon {
    font-size: 2.1rem;
    margin-bottom: 0.7rem;
    opacity: 0.92;
}

.service-title {
    font-size: 1.08rem;
    font-weight: 600;
    color: #D32F2F;
    margin-bottom: 0.3rem;
    letter-spacing: -0.5px;
}

.service-description {
    color: #666;
    font-size: 0.97rem;
    font-weight: 400;
}

/* Contact Section */
.contact {
    background: #fff;
    padding: 3rem 0 2.5rem 0;
}

.contact .section-title {
    margin-bottom: 2rem;
}

.contact-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.2rem;
    max-width: 500px;
    margin: 0 auto;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.1rem 1rem;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    transition: border 0.2s, background 0.2s;
}

.contact-item:hover {
    border: 1.5px solid #FFEB3B;
    background: #fffbe7;
}

.contact-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    color: #D32F2F;
    border-radius: 50%;
    border: 1px solid #f0f0f0;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1rem;
    font-weight: 500;
    color: #222;
    margin-bottom: 0.2rem;
}

.contact-details a {
    color: #D32F2F;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.contact-details a:hover {
    color: #B71C1C;
}

/* Footer */
.footer {
    background: #fff;
    color: #888;
    text-align: center;
    padding: 1.5rem 0 1.2rem 0;
    font-size: 0.93rem;
    border-top: 1px solid #f0f0f0;
    letter-spacing: 0.1px;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 18px;
    right: 18px;
    z-index: 1000;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: #25D366;
    border-radius: 50%;
    box-shadow: none;
    text-decoration: none;
    transition: background 0.2s, transform 0.2s;
    opacity: 0.92;
}

.whatsapp-button:hover {
    background: #1fa855;
    transform: scale(1.07);
}

.whatsapp-button .whatsapp-icon {
    font-size: 1.3rem;
    color: #fff;
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsividade */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .about-text {
        font-size: 1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .logo {
        max-height: 60px;
    }
    
    .header {
        padding: 1.5rem 0;
    }
    
    .hero {
        padding: 3rem 0;
    }
    
    .about, .services, .contact {
        padding: 3rem 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 1.7rem;
    }
    
    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .service-item {
        padding: 1.5rem;
    }
    
    .whatsapp-float {
        bottom: 15px;
        right: 15px;
    }
    
    .whatsapp-button {
        width: 50px;
        height: 50px;
    }
    
    .whatsapp-button .whatsapp-icon {
        font-size: 1.3rem;
    }
}
